# Test Plan: Standard Objects Editing với Standard Overrides

## Mục tiêu
Verify rằng việc edit standard objects hoạt động đúng với cơ chế Standard Overrides.

## Test Cases

### 1. Test UI/UX cho Standard Objects
**Steps:**
1. Mở Settings > Data Model
2. Click vào một standard object (ví dụ: Person, Company)
3. Verify hiển thị warning message về standard object
4. Verify labels hiển thị "Display Name (Singular)" và "Display Name (Plural)"
5. Verify API name fields bị disable và có tooltip rõ ràng
6. Verify icon picker có tooltip giải thích

**Expected Results:**
- Warning message hiển thị rõ ràng
- Labels được đổi tên phù hợp
- API names bị disable với tooltip giải thích
- Icon picker có tooltip

### 2. Test Validation cho Standard Objects
**Steps:**
1. Mở form edit standard object
2. Thử đặt labelSingular và labelPlural giống nhau
3. Submit form

**Expected Results:**
- Validation error hiển thị cho labels giống nhau
- Không có validation error cho API names (vì chúng bị disable)

### 3. Test Save Standard Object Changes
**Steps:**
1. Mở form edit standard object
2. Thay đổi labelSingular, labelPlural, description, icon
3. Submit form
4. Verify changes được lưu

**Expected Results:**
- Changes được lưu thành công
- API names không thay đổi
- Display labels được update
- Backend tạo standardOverrides entry

### 4. Test Custom Objects (Regression)
**Steps:**
1. Mở form edit custom object
2. Verify không có warning message
3. Verify API names có thể edit
4. Verify validation hoạt động bình thường

**Expected Results:**
- Không có warning message
- API names có thể edit
- Validation hoạt động như cũ

## Backend Verification

### Check Standard Overrides Storage
```sql
-- Kiểm tra standardOverrides được lưu đúng
SELECT id, "nameSingular", "namePlural", "labelSingular", "labelPlural", "standardOverrides" 
FROM metadata."objectMetadata" 
WHERE "isCustom" = false 
AND "standardOverrides" IS NOT NULL;
```

### Check API Response
- Verify API trả về đúng resolved values (overrides > original)
- Verify API names không thay đổi
- Verify display labels được update

## Files Changed
- `SettingsDataModelObjectAboutForm.tsx` - UI improvements
- `settingsDataModelObjectAboutFormSchema.ts` - Validation logic
- `SettingsUpdateDataModelObjectAboutForm.tsx` - Form configuration
- Test files updated

## Success Criteria
✅ All test cases pass
✅ No TypeScript errors
✅ No regression in custom objects functionality
✅ Standard objects can be customized safely
✅ API names remain immutable for standard objects
✅ User experience is clear and intuitive
